// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
  // Further reading:
  // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
  // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
  url      = env("DATABASE_URL")
}

// Necessary for Next auth
model User {
  id            String          @id @default(cuid())
  name          String?
  email         String?         @unique
  emailVerified DateTime?
  image         String?
  role          Role            @default(USER)
  password      String?
  passwordSlat  String?
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]

  createdTasks  Task[] @relation("taskCreator")
  assignedTasks TaskAssignee[] // 分配的任务关系

  taskApplicationsAsPublisher TaskApplication[] @relation("TaskPublisher")
  taskApplicationsAsApplicant TaskApplication[] @relation("TaskApplicant")
  givenEvaluations            Evaluation[]      @relation("Evaluator")
  receivedEvaluations         Evaluation[]      @relation("Evaluated")

  createdAt           DateTime             @default(now())
  updatedAt           DateTime?            @updatedAt
  Evaluation          Evaluation[]
  UserEvaluationStats UserEvaluationStats?
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Task {
  id            String     @id @default(cuid()) // 任务唯一标识
  title         String // 任务标题
  description   String? // 任务描述
  budget        Float? // 任务预算
  status        TaskStatus @default(PENDING) // 任务状态（待接单、进行中、完成等）
  creator       User       @relation("taskCreator", fields: [creatorId], references: [id]) // 创建者
  creatorId     String // 创建者ID
  assignees     TaskAssignee[] // 接单人关系
  createdAt     DateTime   @default(now()) // 创建时间
  updatedAt     DateTime   @updatedAt // 更新时间
  startTime     DateTime? // 预计开始时间
  endTime       DateTime? // 预计结束时间
  completedAt   DateTime? // 实际完成时间
  robotType     String? // 所需机器人类型
  attachmentUrl String[] // 附件或视频参考链接

  Request     TaskApplication[] @relation("TaskRequest")
  evaluations Evaluation[]
}

model TaskAssignee {
  taskId        String
  userId        String
  task          Task    @relation(fields: [taskId], references: [id])
  user          User    @relation(fields: [userId], references: [id])
  assigneeStatus TaskAssigneeStatus @default(PENDING)
  @@id([taskId, userId]) // 联合主键，确保一个任务和用户之间的关系唯一
}

model TaskApplication {
  id              String                @id @default(cuid())
  taskId          String // 任务 ID
  applicantId     String // 申请人 (执行者) 的 ID
  taskPublisherId String // 发布者 (发布任务的人) 的 ID
  status          TaskApplicationState? @default(PENDING)
  applicationDate DateTime?             @default(now())
  createdAt       DateTime?             @default(now()) // 创建时间
  updatedAt       DateTime?             @updatedAt

  // 关联申请人和任务发布者
  applicant     User? @relation("TaskApplicant", fields: [applicantId], references: [id])
  taskPublisher User? @relation("TaskPublisher", fields: [taskPublisherId], references: [id])
  task          Task? @relation("TaskRequest", fields: [taskId], references: [id])
}

// 评价表（核心表）
model Evaluation {
  id          String @id @default(cuid())
  taskId      String
  evaluatorId String // 评价者ID
  evaluatedId String // 被评价者ID

  // 评分字段（1-5分制）
  overallScore Int @default(0) // 总体评分

  // 操作员评价任务发布方的维度
  taskClarityScore   Int? // 任务描述清晰度评分
  environmentScore   Int? // 环境条件评分  
  robotStatusScore   Int? // 机器人状态评分
  communicationScore Int? // 负责人沟通评分

  // 任务发布方评价操作员的维度
  skillScore      Int? // 操作熟练度评分
  qualityScore    Int? // 任务完成质量评分
  attitudeScore   Int? // 沟通态度评分
  complianceScore Int? // 遵守规范评分

  // 文字评价
  comment String? // 详细文字评价

  // 时间戳
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  task      Task    @relation(fields: [taskId], references: [id], onDelete: Cascade)
  evaluator User    @relation("Evaluator", fields: [evaluatorId], references: [id])
  evaluated User    @relation("Evaluated", fields: [evaluatedId], references: [id])
  User      User?   @relation(fields: [userId], references: [id])
  userId    String?

  // 约束：确保每个任务中，同一个评价者对同一个被评价者只能有一条评价记录
  @@unique([taskId, evaluatorId, evaluatedId])
  @@map("evaluations")
}

// 评价统计表（可选，用于缓存用户的评价统计信息）
model UserEvaluationStats {
  id     String @id @default(cuid())
  userId String @unique

  // 作为被评价方的统计
  totalEvaluationsReceived Int   @default(0)
  averageScore             Float @default(0)

  // 按角色分组的统计
  // 作为任务发布方被评价的统计
  asPublisherCount         Int   @default(0)
  asPublisherAvgScore      Float @default(0)
  asPublisherTaskClarity   Float @default(0)
  asPublisherEnvironment   Float @default(0)
  asPublisherRobotStatus   Float @default(0)
  asPublisherCommunication Float @default(0)

  // 作为操作员被评价的统计
  asOperatorCount      Int   @default(0)
  asOperatorAvgScore   Float @default(0)
  asOperatorSkill      Float @default(0)
  asOperatorQuality    Float @default(0)
  asOperatorAttitude   Float @default(0)
  asOperatorCompliance Float @default(0)

  updatedAt DateTime @updatedAt

  // 关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_evaluation_stats")
}

enum EvaluationType {
  OPERATOR_TO_PUBLISHER // 操作员评价任务发布方
  PUBLISHER_TO_OPERATOR // 任务发布方评价操作员
}

enum Role {
  USER // 普通用户（操作者）
  MANAGER // 发布者
  ADMIN // 管理员
}

enum TaskStatus {
  PENDING // 待接单
  IN_PROGRESS // 进行中
  COMPLETED // 已完成
  CANCELLED // 已取消
}

enum TaskApplicationState {
  PENDING // 待审核
  IN_PROGRESS // 审核通过，任务进行中
  CANCELLED // 已取消
  EXPIRED // 已过期
  COMPLETED // 已完成
}

enum TaskAssigneeStatus {
  PENDING       // 任务分配给用户，但用户尚未开始
  IN_PROGRESS   // 用户正在进行任务
  COMPLETED     // 用户已完成任务
}
