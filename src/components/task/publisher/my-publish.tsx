'use client'

import TaskItem from "@/components/teleop/task-item"
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsTrigger } from "@/components/ui/tabs";
import { useApplication, type ApplyType } from "@/hooks/use-application";
import { TaskApplicationState, TaskStatus } from "@prisma/client";
import { TabsList } from "@radix-ui/react-tabs";
import { HandCoins, IdCard, Search } from "lucide-react";
import { useEffect, useState } from "react";
import useStatusGet from "@/hooks/use-state-get";
import { useTaskQuery } from "@/hooks/use-task-query";

export default function MyPublish() {
  const [clicked, setClicked] = useState<string>('');
  const [search, setSearch] = useState<string>('');
  const [selectedTask, setSelectedTask] = useState<ApplyType | null>(null);
  const [activeTab, setActiveTab] = useState(' ');

  const {
    getTaskStatusColor,
    getTaskStatusText
  } = useStatusGet()

  // const { byApplicantId: list, setFilter, isFetching } = useApplication({})
  const { myPublished } = useTaskQuery()
  console.log(myPublished);
  
  // 等待查询成功且不在加载中
  // useEffect(() => {
  //   if (!isFetching && list.length > 0) {
  //     const first = list[0];
  //     if (first) {
  //       setSelectedTask(first);
  //       setClicked(first.id);
  //     } else {
  //       setSelectedTask(null);
  //     }
  //   }
  // }, [list, isFetching])

  function handleClick(task: ApplyType) {
    setSelectedTask(task)
    setClicked(task.id);
  }

  // function onSearch() {
  //   setFilter(prev => ({
  //     ...prev,
  //     taskTitle: search
  //   }))
  // }

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // 当切换 tab 时，清除当前选中的任务（可选）
    // setClicked(null);
    // setFilter(prev => ({
    //   ...prev,
    //   status: value
    // }));
  };

  return (
    <main className="m-full h-full bg-gray-50">
      <div className="max-w-[1480px] m-auto mt-8 ">
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-8">
          <div className="lg:col-span-1">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full sticky top-8">
              <TabsList className="flex flex-col w-full gap-2">
                <TabsTrigger value=" " className="w-full">
                  全部
                </TabsTrigger>
                <TabsTrigger value="PENDING" className="w-full">
                  待接单
                </TabsTrigger>
                <TabsTrigger value="IN_PROGRESS" className="w-full">
                  进行中
                </TabsTrigger>
                <TabsTrigger value="CANCELLED" className="w-full">
                  已取消
                </TabsTrigger>
                <TabsTrigger value="COMPLETED" className="w-full">
                  已完成
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          {/* 任务列表 */}
          <div className="lg:col-span-2 space-y-4">
            {/* {list?.map(task => (
              <TaskItem
                key={task.id}
                data={task}
                clicked={task.id === clicked}
                onClick={handleClick}
                getText={getTaskStatusText}
                getColor={getTaskStatusColor}
              />
            ))} */}
          </div>
          <div className="lg:col-span-2">
            {selectedTask && <SelectTask selectedTask={selectedTask} />}
          </div>
        </div>
      </div>
    </main>
  )
}

function SelectTask({ selectedTask }: { selectedTask: ApplyType }) {
  return (
    <>
      <div className="bg-white shadow-xl shadow-gray-100 rounded-sm sticky top-8">
        <div className="p-6">
          <div className="flex items-center justify-between mb-1">
            <h2 className="text-2xl font-semibold text-gray-900">{selectedTask.task.title}</h2>
          </div>
          <div className="mb-3 flex items-center gap-2">
            <span className="text-xs text-gray-600">
            </span>
          </div>

          <div className="flex items-center gap-2">
            <HandCoins className="w-4 h-4 text-gray-600" />
            <span className="text-md text-gray-600">
              {selectedTask.task.budget}
            </span>
          </div>
          <div className="mb-3 flex items-center gap-2">
            <IdCard className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-600">不限经验</span>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl text-gray-800">任务详情</h1>
            <p className="mt-2 pr-2 text-md text-gray-600">
              {selectedTask.task.description}
            </p>
          </div>

          {/* 操作按钮 */}
          <TaskActions status={selectedTask.task.status as TaskStatus} />
        </div>
      </div>
    </>
  )
}



function TaskActions({ status }: { status: TaskStatus }) {
  const statusButtonMap = {
    [TaskStatus.PENDING]: ['编辑', '下架', '删除'],
    [TaskStatus.IN_PROGRESS]: ['编辑', '下架'],
    [TaskStatus.COMPLETED]: ['删除'],
    [TaskStatus.CANCELLED]: ['删除'],
  };
  const buttons = statusButtonMap[status] || [];

  return (
    <div>
      {buttons.map((label, index) => (
        <Button key={index}>{label}</Button>
      ))}
    </div>
  );
};

function OldSearch() {
  return (
    {/* <section className='sticky top-0 h-20 flex items-center m-auto bg-white'>
        <div className='max-w-7xl w-full m-auto px-2'>
          <div className="flex items-center justify-between space-x-4">
            <div className="relative flex px-2 py-1 gap-2 border-2 border-gray-100 rounded-lg no-focus-ring focus-within:border-primary">
              <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="搜索任务..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 pr-4 py-2 border-none bg-transparent shadow-none focus:outline-none"
              />
              <div className="flex items-center">
                <Button className="rounded-sm" size='sm' onClick={onSearch}>搜索</Button>
              </div>
            </div>
          </div>
        </div>
      </section> */}
  )
}
