import { useState } from 'react';
import { api } from "@/trpc/react";
import type { Task } from '@prisma/client';
import { keepPreviousData } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';

export interface TaskPro extends Task {
  creator: { id: string; name: string; role: string, image: string };
}

export type HandleFilterType = (key: string, value: string) => Promise<void>

export function useTaskQuery(initialSearchValue = '', initialOrder: 'asc' | 'desc' = 'desc') {
  const { data } = useSession()

  const [filters, setFilters] = useState({
    title: initialSearchValue,
    order: initialOrder,
    budget: '',
    publishedTime: '',
  });

  const queryResult = api.task.queryTasks.useQuery(
    { ...filters },
    {
      enabled: true,
      refetchOnWindowFocus: false,
      placeholderData: keepPreviousData,
    }
  );

  const handleFilter = async (key: string, value: string) => {
    setFilters(filter => ({
      ...filter,
      [key]: value
    }));
  }

  const getPublish = api.task.getByPublished.useQuery({
    publisherId: data?.user.id ?? ''
  }, {
    enabled: !!data?.user.id,
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData,
  })

  return {
    // list: (queryResult.data ?? []) as TaskPro[],
    list: [] as TaskPro[],
    order: filters.order,
    myPublished: getPublish.data ?? [],
    setFilters,
    handleFilter
  };
};
